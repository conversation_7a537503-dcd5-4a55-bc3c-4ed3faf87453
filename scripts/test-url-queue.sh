#!/bin/bash

# 测试URL队列功能的脚本

BASE_URL="http://localhost:8080/api/v1"

echo "=== 爬虫任务管理服务 - URL队列功能测试 ==="
echo

# 1. 创建测试任务
echo "1. 创建测试任务..."
TASK_RESPONSE=$(curl -s -X POST ${BASE_URL}/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "URL队列测试任务",
    "description": "测试URL队列功能的任务",
    "initial_urls": [
      {
        "url": "https://httpbin.org/get?page=1",
        "method": "GET"
      },
      {
        "url": "https://httpbin.org/get?page=2",
        "method": "GET"
      },
      {
        "url": "https://httpbin.org/post",
        "method": "POST",
        "body": "{\"test\": \"data\"}"
      }
    ],
    "priority": 2,
    "downloader_name": "test_downloader",
    "parser_name": "test_parser"
  }')

echo "任务创建响应:"
echo $TASK_RESPONSE | jq '.'
echo

# 提取任务ID
TASK_ID=$(echo $TASK_RESPONSE | jq -r '.task.id')
echo "任务ID: $TASK_ID"
echo

# 2. 获取任务URL统计
echo "2. 获取任务URL统计..."
curl -s ${BASE_URL}/tasks/${TASK_ID}/urls/stats | jq '.'
echo

# 3. 查询所有URL队列
echo "3. 查询所有URL队列..."
curl -s "${BASE_URL}/urls?limit=10" | jq '.'
echo

# 4. 查询特定任务的URL
echo "4. 查询特定任务的URL..."
curl -s "${BASE_URL}/urls?task_id=${TASK_ID}" | jq '.'
echo

# 5. 获取待处理的URL
echo "5. 获取待处理的URL..."
PENDING_URLS=$(curl -s "${BASE_URL}/urls/pending?limit=5")
echo $PENDING_URLS | jq '.'
echo

# 6. 更新第一个URL的状态为processing
echo "6. 更新URL状态为processing..."
FIRST_URL_ID=$(echo $PENDING_URLS | jq -r '.data.urls[0].id')
if [ "$FIRST_URL_ID" != "null" ] && [ "$FIRST_URL_ID" != "" ]; then
  curl -s -X PUT ${BASE_URL}/urls/${FIRST_URL_ID}/status \
    -H "Content-Type: application/json" \
    -d '{
      "status": "processing"
    }' | jq '.'
  echo
fi

# 7. 更新第二个URL的状态为failed
echo "7. 更新URL状态为failed..."
SECOND_URL_ID=$(echo $PENDING_URLS | jq -r '.data.urls[1].id')
if [ "$SECOND_URL_ID" != "null" ] && [ "$SECOND_URL_ID" != "" ]; then
  curl -s -X PUT ${BASE_URL}/urls/${SECOND_URL_ID}/status \
    -H "Content-Type: application/json" \
    -d '{
      "status": "failed",
      "error_message": "测试错误信息"
    }' | jq '.'
  echo
fi

# 8. 再次获取任务URL统计
echo "8. 更新后的任务URL统计..."
curl -s ${BASE_URL}/tasks/${TASK_ID}/urls/stats | jq '.'
echo

# 9. 查询失败的URL
echo "9. 查询失败的URL..."
curl -s "${BASE_URL}/urls?task_id=${TASK_ID}&status=failed" | jq '.'
echo

# 10. 重置失败的URL
echo "10. 重置失败的URL..."
curl -s -X POST ${BASE_URL}/tasks/${TASK_ID}/urls/reset \
  -H "Content-Type: application/json" \
  -d '{
    "max_retry_count": 3
  }' | jq '.'
echo

# 11. 最终的任务URL统计
echo "11. 重置后的任务URL统计..."
curl -s ${BASE_URL}/tasks/${TASK_ID}/urls/stats | jq '.'
echo

echo "=== 测试完成 ==="
