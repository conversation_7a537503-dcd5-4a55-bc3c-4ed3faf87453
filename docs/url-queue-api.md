# URL队列API文档

## 概述

URL队列功能允许将任务的InitialURLs保存到数据库中，供下载器服务读取和处理。当创建任务时，系统会自动将InitialURLs拆分并插入到URL队列表中，同时发布任务创建事件。

## 数据库表结构

### url_queue表

```sql
CREATE TABLE url_queue (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(255) NOT NULL COMMENT '关联的任务ID',
    url VARCHAR(2048) NOT NULL COMMENT '待下载的URL',
    method ENUM('GET', 'POST') NOT NULL COMMENT 'HTTP请求方法',
    body TEXT COMMENT 'POST请求体，仅当method为POST时使用',
    priority INT NOT NULL COMMENT '优先级，1=低，2=中，3=高',
    status ENUM('pending', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending' COMMENT 'URL处理状态',
    retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_status_priority (status, priority DESC),
    INDEX idx_created_at (created_at),
    INDEX idx_status_retry (status, retry_count)
);
```

## API接口

### 1. 获取待处理的URL

**请求**
```
GET /api/v1/urls/pending?limit=10
```

**参数**
- `limit` (可选): 返回的URL数量限制，默认10

**响应**
```json
{
  "success": true,
  "message": "获取待处理URL成功",
  "data": {
    "urls": [
      {
        "id": 1,
        "task_id": "uuid",
        "url": "https://example.com",
        "method": "GET",
        "body": "",
        "priority": 2,
        "status": "pending",
        "retry_count": 0,
        "error_message": "",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "count": 1
  }
}
```

### 2. 更新URL状态

**请求**
```
PUT /api/v1/urls/:id/status
```

**请求体**
```json
{
  "status": "processing",
  "error_message": "可选的错误信息"
}
```

**参数**
- `status`: URL状态，可选值：pending, processing, completed, failed
- `error_message` (可选): 错误信息，当status为failed时使用

**响应**
```json
{
  "success": true,
  "message": "URL状态更新成功"
}
```

### 3. 查询URL队列

**请求**
```
GET /api/v1/urls?task_id=xxx&status=pending&priority=2&limit=10&offset=0
```

**参数**
- `task_id` (可选): 任务ID
- `status` (可选): URL状态
- `priority` (可选): 优先级 (1-3)
- `limit` (可选): 返回数量限制
- `offset` (可选): 偏移量

**响应**
```json
{
  "success": true,
  "message": "查询URL队列成功",
  "data": {
    "urls": [...],
    "count": 10,
    "query": {
      "task_id": "xxx",
      "status": "pending",
      "priority": 2,
      "limit": 10,
      "offset": 0
    }
  }
}
```

### 4. 获取任务URL统计

**请求**
```
GET /api/v1/tasks/:taskId/urls/stats
```

**响应**
```json
{
  "success": true,
  "message": "获取任务URL统计成功",
  "data": {
    "task_id": "uuid",
    "total": 100,
    "stats": {
      "pending": 50,
      "processing": 20,
      "completed": 25,
      "failed": 5
    }
  }
}
```

### 5. 重置失败的URL

**请求**
```
POST /api/v1/tasks/:taskId/urls/reset
```

**请求体**
```json
{
  "max_retry_count": 3
}
```

**参数**
- `max_retry_count`: 最大重试次数，只有重试次数小于此值的失败URL才会被重置

**响应**
```json
{
  "success": true,
  "message": "重置失败URL成功"
}
```

## 事件发布

当创建任务时，系统会发布任务创建事件到Dapr Pub/Sub：

**主题**: `task.created`

**事件数据**
```json
{
  "task_id": "uuid",
  "task_name": "任务名称",
  "priority": 2,
  "url_count": 10,
  "created_at": "2024-01-01T00:00:00Z"
}
```

## 使用流程

### 1. 任务管理服务
1. 接收创建任务请求
2. 保存任务到Dapr状态存储
3. 将InitialURLs插入到url_queue表
4. 发布任务创建事件

### 2. 下载器服务（待实现）
1. 订阅任务创建事件
2. 调用 `/api/v1/urls/pending` 获取待处理URL
3. 更新URL状态为processing
4. 执行下载操作
5. 根据结果更新URL状态为completed或failed

## 测试

使用提供的测试脚本：

```bash
# 测试URL队列功能
make test-url-queue

# 或直接运行脚本
./scripts/test-url-queue.sh
```

## 注意事项

1. **事务安全**: URL插入操作使用数据库事务确保一致性
2. **优先级排序**: 获取待处理URL时按优先级降序、创建时间升序排列
3. **重试机制**: 支持失败URL的重试，可通过重置接口重新处理
4. **状态管理**: URL状态变更会自动更新时间戳
5. **错误处理**: 失败的URL会记录错误信息和重试次数
