package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"golden_crawler/config"
	"golden_crawler/internal/models"

	"github.com/dapr/go-sdk/client"
	"github.com/google/uuid"
)

// TaskService 定义任务服务接口
type TaskService interface {
	CreateTask(ctx context.Context, req *models.CreateTaskRequest) (*models.CrawlerTask, error)
	GetTask(ctx context.Context, taskID string) (*models.CrawlerTask, error)
	UpdateTaskStatus(ctx context.Context, taskID string, status models.TaskStatus) error
	ListTasks(ctx context.Context, limit, offset int) ([]*models.CrawlerTask, error)
	DeleteTask(ctx context.Context, taskID string) error
	Close() error
}

// taskServiceImpl 任务服务实现
type taskServiceImpl struct {
	daprClient      client.Client
	urlQueueService URLQueueService
	config          *config.Config
}

// NewTaskService 创建新的任务服务实例
func NewTaskService(cfg *config.Config) (TaskService, error) {
	// 创建Dapr客户端
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	// 创建URL队列服务
	urlQueueService, err := NewURLQueueService(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建URL队列服务失败: %w", err)
	}

	return &taskServiceImpl{
		daprClient:      daprClient,
		urlQueueService: urlQueueService,
		config:          cfg,
	}, nil
}

// CreateTask 创建新的爬虫任务
func (s *taskServiceImpl) CreateTask(ctx context.Context, req *models.CreateTaskRequest) (*models.CrawlerTask, error) {
	// 生成唯一任务ID
	taskID := uuid.New().String()

	// 创建任务对象
	task := &models.CrawlerTask{
		ID:             taskID,
		Name:           req.Name,
		Description:    req.Description,
		InitialURLs:    req.InitialURLs,
		Priority:       req.Priority,
		DownloaderName: req.DownloaderName,
		ParserName:     req.ParserName,
		Status:         models.StatusPending,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 1. 保存任务到Dapr状态存储
	taskKey := fmt.Sprintf("task:%s", taskID)
	taskData, err := json.Marshal(task)
	if err != nil {
		return nil, fmt.Errorf("序列化任务数据失败: %w", err)
	}

	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskKey, taskData, nil)
	if err != nil {
		return nil, fmt.Errorf("保存任务到状态存储失败: %w", err)
	}

	// 保存任务ID到任务列表（用于ListTasks）
	taskListKey := "task:list"
	existingList, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
	if err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %w", err)
	}

	var taskIDs []string
	if existingList.Value != nil {
		err = json.Unmarshal(existingList.Value, &taskIDs)
		if err != nil {
			return nil, fmt.Errorf("解析任务列表失败: %w", err)
		}
	}

	// 添加新任务ID到列表
	taskIDs = append(taskIDs, taskID)
	taskListData, err := json.Marshal(taskIDs)
	if err != nil {
		return nil, fmt.Errorf("序列化任务列表失败: %w", err)
	}

	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskListKey, taskListData, nil)
	if err != nil {
		return nil, fmt.Errorf("保存任务列表失败: %w", err)
	}

	// 2. 将InitialURLs添加到URL队列
	err = s.urlQueueService.AddURLs(ctx, taskID, req.InitialURLs, req.Priority)
	if err != nil {
		// 如果添加URL失败，需要清理已创建的任务
		s.daprClient.DeleteState(ctx, s.config.Dapr.StateStore, taskKey, nil)
		s.daprClient.DeleteState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
		return nil, fmt.Errorf("添加URL到队列失败: %w", err)
	}

	// 3. 发布任务创建事件到Dapr pub/sub
	taskEvent := &models.TaskCreatedEvent{
		TaskID:    taskID,
		TaskName:  req.Name,
		Priority:  req.Priority,
		URLCount:  len(req.InitialURLs),
		CreatedAt: time.Now(),
	}

	eventData, err := json.Marshal(taskEvent)
	if err != nil {
		fmt.Printf("序列化任务创建事件失败: %v\n", err)
	} else {
		err = s.daprClient.PublishEvent(ctx, s.config.Dapr.PubSubName, "task.created", eventData)
		if err != nil {
			fmt.Printf("发布任务创建事件失败: %v\n", err)
		} else {
			fmt.Printf("成功发布任务创建事件: %s\n", taskID)
		}
	}

	fmt.Printf("成功创建任务并保存到状态存储: %s，添加了 %d 个URL到队列\n", taskID, len(req.InitialURLs))

	return task, nil
}

// GetTask 根据任务ID获取任务信息
func (s *taskServiceImpl) GetTask(ctx context.Context, taskID string) (*models.CrawlerTask, error) {
	taskKey := fmt.Sprintf("task:%s", taskID)

	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskKey, nil)
	if err != nil {
		return nil, fmt.Errorf("从状态存储获取任务失败: %w", err)
	}

	if result.Value == nil {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	var task models.CrawlerTask
	err = json.Unmarshal(result.Value, &task)
	if err != nil {
		return nil, fmt.Errorf("解析任务数据失败: %w", err)
	}

	fmt.Printf("成功获取任务: %s\n", taskID)

	return &task, nil
}

// UpdateTaskStatus 更新任务状态
func (s *taskServiceImpl) UpdateTaskStatus(ctx context.Context, taskID string, status models.TaskStatus) error {
	// 首先获取现有任务
	task, err := s.GetTask(ctx, taskID)
	if err != nil {
		return fmt.Errorf("获取任务失败: %w", err)
	}

	// 更新任务状态和更新时间
	task.Status = status
	task.UpdatedAt = time.Now()

	// 保存更新后的任务到状态存储
	taskKey := fmt.Sprintf("task:%s", taskID)
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务数据失败: %w", err)
	}

	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskKey, taskData, nil)
	if err != nil {
		return fmt.Errorf("保存任务状态失败: %w", err)
	}

	// TODO: 2. 发布任务状态变更事件

	fmt.Printf("成功更新任务状态: %s -> %s\n", taskID, status)

	return nil
}

// ListTasks 获取任务列表
func (s *taskServiceImpl) ListTasks(ctx context.Context, limit, offset int) ([]*models.CrawlerTask, error) {
	// 获取任务ID列表
	taskListKey := "task:list"
	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
	if err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %w", err)
	}

	if result.Value == nil {
		return []*models.CrawlerTask{}, nil
	}

	var taskIDs []string
	err = json.Unmarshal(result.Value, &taskIDs)
	if err != nil {
		return nil, fmt.Errorf("解析任务列表失败: %w", err)
	}

	// 计算分页
	totalTasks := len(taskIDs)
	if offset >= totalTasks {
		return []*models.CrawlerTask{}, nil
	}

	end := offset + limit
	if end > totalTasks {
		end = totalTasks
	}

	// 获取分页范围内的任务ID
	pagedTaskIDs := taskIDs[offset:end]

	// 获取每个任务的详细信息
	tasks := make([]*models.CrawlerTask, 0, len(pagedTaskIDs))
	for _, taskID := range pagedTaskIDs {
		task, err := s.GetTask(ctx, taskID)
		if err != nil {
			// 如果某个任务获取失败，记录错误但继续处理其他任务
			fmt.Printf("获取任务 %s 失败: %v\n", taskID, err)
			continue
		}
		tasks = append(tasks, task)
	}

	fmt.Printf("成功获取任务列表: limit=%d, offset=%d, 返回%d个任务\n", limit, offset, len(tasks))

	return tasks, nil
}

// DeleteTask 删除任务
func (s *taskServiceImpl) DeleteTask(ctx context.Context, taskID string) error {
	// 1. 检查任务是否存在
	_, err := s.GetTask(ctx, taskID)
	if err != nil {
		return fmt.Errorf("任务不存在: %w", err)
	}

	// 2. 从状态存储删除任务
	taskKey := fmt.Sprintf("task:%s", taskID)
	err = s.daprClient.DeleteState(ctx, s.config.Dapr.StateStore, taskKey, nil)
	if err != nil {
		return fmt.Errorf("删除任务失败: %w", err)
	}

	// 3. 从任务列表中移除任务ID
	taskListKey := "task:list"
	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
	if err != nil {
		return fmt.Errorf("获取任务列表失败: %w", err)
	}

	if result.Value != nil {
		var taskIDs []string
		err = json.Unmarshal(result.Value, &taskIDs)
		if err != nil {
			return fmt.Errorf("解析任务列表失败: %w", err)
		}

		// 移除指定的任务ID
		updatedTaskIDs := make([]string, 0, len(taskIDs))
		for _, id := range taskIDs {
			if id != taskID {
				updatedTaskIDs = append(updatedTaskIDs, id)
			}
		}

		// 保存更新后的任务列表
		taskListData, err := json.Marshal(updatedTaskIDs)
		if err != nil {
			return fmt.Errorf("序列化任务列表失败: %w", err)
		}

		err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskListKey, taskListData, nil)
		if err != nil {
			return fmt.Errorf("保存任务列表失败: %w", err)
		}
	}

	// TODO: 4. 清理相关资源
	// TODO: 5. 发布任务删除事件

	fmt.Printf("成功删除任务: %s\n", taskID)

	return nil
}

// Close 关闭Dapr客户端连接和URL队列服务
func (s *taskServiceImpl) Close() error {
	if s.daprClient != nil {
		s.daprClient.Close()
	}
	if s.urlQueueService != nil {
		s.urlQueueService.Close()
	}
	return nil
}
