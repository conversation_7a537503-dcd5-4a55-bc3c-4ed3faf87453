package services

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"golden_crawler/config"
	"golden_crawler/internal/models"

	_ "github.com/go-sql-driver/mysql"
)

// URLQueueService 定义URL队列服务接口
type URLQueueService interface {
	// 添加URL到队列
	AddURLs(ctx context.Context, taskID string, urls []models.ReqURL, priority models.TaskPriority) error

	// 获取待处理的URL
	GetPendingURLs(ctx context.Context, limit int) ([]*models.URLQueueItem, error)

	// 更新URL状态
	UpdateURLStatus(ctx context.Context, urlID int64, status models.URLStatus, errorMessage string) error

	// 获取任务的URL统计
	GetTaskURLStats(ctx context.Context, taskID string) (map[models.URLStatus]int, error)

	// 查询URL队列
	QueryURLs(ctx context.Context, query *models.URLQueueQuery) ([]*models.URLQueueItem, error)

	// 重置失败的URL为待处理状态
	ResetFailedURLs(ctx context.Context, taskID string, maxRetryCount int) error

	// 关闭服务
	Close() error
}

// urlQueueServiceImpl URL队列服务实现
type urlQueueServiceImpl struct {
	db     *sql.DB
	config *config.Config
}

// NewURLQueueService 创建新的URL队列服务实例
func NewURLQueueService(cfg *config.Config) (URLQueueService, error) {
	// 构建MySQL连接字符串
	dsn := "root:password@tcp(localhost:3306)/crawler_db?charset=utf8mb4&parseTime=True&loc=Local"

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL数据库失败: %w", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("MySQL数据库连接测试失败: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	return &urlQueueServiceImpl{
		db:     db,
		config: cfg,
	}, nil
}

// AddURLs 添加URL到队列
func (s *urlQueueServiceImpl) AddURLs(ctx context.Context, taskID string, urls []models.ReqURL, priority models.TaskPriority) error {
	if len(urls) == 0 {
		return nil
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 准备插入语句
	stmt, err := tx.PrepareContext(ctx, `
		INSERT INTO url_queue (task_id, url, method, body, priority, status, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
	`)
	if err != nil {
		return fmt.Errorf("准备插入语句失败: %w", err)
	}
	defer stmt.Close()

	// 批量插入URL
	for _, url := range urls {
		_, err := stmt.ExecContext(ctx, taskID, url.URL, string(url.Method), url.Body, int(priority), string(models.URLStatusPending))
		if err != nil {
			return fmt.Errorf("插入URL失败: %w", err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	fmt.Printf("成功添加 %d 个URL到队列，任务ID: %s\n", len(urls), taskID)
	return nil
}

// GetPendingURLs 获取待处理的URL
func (s *urlQueueServiceImpl) GetPendingURLs(ctx context.Context, limit int) ([]*models.URLQueueItem, error) {
	query := `
		SELECT id, task_id, url, method, body, priority, status, retry_count, error_message, created_at, updated_at
		FROM url_queue 
		WHERE status = ? 
		ORDER BY priority DESC, created_at ASC 
		LIMIT ?
	`

	rows, err := s.db.QueryContext(ctx, query, string(models.URLStatusPending), limit)
	if err != nil {
		return nil, fmt.Errorf("查询待处理URL失败: %w", err)
	}
	defer rows.Close()

	var urls []*models.URLQueueItem
	for rows.Next() {
		url := &models.URLQueueItem{}
		var body, errorMessage sql.NullString

		err := rows.Scan(
			&url.ID, &url.TaskID, &url.URL, &url.Method, &body,
			&url.Priority, &url.Status, &url.RetryCount, &errorMessage,
			&url.CreatedAt, &url.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描URL数据失败: %w", err)
		}

		if body.Valid {
			url.Body = body.String
		}
		if errorMessage.Valid {
			url.ErrorMessage = errorMessage.String
		}

		urls = append(urls, url)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历URL数据失败: %w", err)
	}

	return urls, nil
}

// UpdateURLStatus 更新URL状态
func (s *urlQueueServiceImpl) UpdateURLStatus(ctx context.Context, urlID int64, status models.URLStatus, errorMessage string) error {
	var query string
	var args []interface{}

	if status == models.URLStatusFailed && errorMessage != "" {
		query = `
			UPDATE url_queue 
			SET status = ?, error_message = ?, retry_count = retry_count + 1, updated_at = NOW()
			WHERE id = ?
		`
		args = []interface{}{string(status), errorMessage, urlID}
	} else {
		query = `
			UPDATE url_queue 
			SET status = ?, updated_at = NOW()
			WHERE id = ?
		`
		args = []interface{}{string(status), urlID}
	}

	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("更新URL状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("URL不存在: %d", urlID)
	}

	fmt.Printf("成功更新URL状态: ID=%d, Status=%s\n", urlID, status)
	return nil
}

// GetTaskURLStats 获取任务的URL统计
func (s *urlQueueServiceImpl) GetTaskURLStats(ctx context.Context, taskID string) (map[models.URLStatus]int, error) {
	query := `
		SELECT status, COUNT(*) as count
		FROM url_queue 
		WHERE task_id = ?
		GROUP BY status
	`

	rows, err := s.db.QueryContext(ctx, query, taskID)
	if err != nil {
		return nil, fmt.Errorf("查询任务URL统计失败: %w", err)
	}
	defer rows.Close()

	stats := make(map[models.URLStatus]int)
	for rows.Next() {
		var status string
		var count int

		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("扫描统计数据失败: %w", err)
		}

		stats[models.URLStatus(status)] = count
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历统计数据失败: %w", err)
	}

	return stats, nil
}

// QueryURLs 查询URL队列
func (s *urlQueueServiceImpl) QueryURLs(ctx context.Context, query *models.URLQueueQuery) ([]*models.URLQueueItem, error) {
	// 构建动态查询
	sqlQuery := `
		SELECT id, task_id, url, method, body, priority, status, retry_count, error_message, created_at, updated_at
		FROM url_queue WHERE 1=1
	`
	var args []interface{}

	if query.TaskID != "" {
		sqlQuery += " AND task_id = ?"
		args = append(args, query.TaskID)
	}

	if query.Status != "" {
		sqlQuery += " AND status = ?"
		args = append(args, string(query.Status))
	}

	if query.Priority > 0 {
		sqlQuery += " AND priority = ?"
		args = append(args, int(query.Priority))
	}

	sqlQuery += " ORDER BY priority DESC, created_at ASC"

	if query.Limit > 0 {
		sqlQuery += " LIMIT ?"
		args = append(args, query.Limit)

		if query.Offset > 0 {
			sqlQuery += " OFFSET ?"
			args = append(args, query.Offset)
		}
	}

	rows, err := s.db.QueryContext(ctx, sqlQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询URL队列失败: %w", err)
	}
	defer rows.Close()

	var urls []*models.URLQueueItem
	for rows.Next() {
		url := &models.URLQueueItem{}
		var body, errorMessage sql.NullString

		err := rows.Scan(
			&url.ID, &url.TaskID, &url.URL, &url.Method, &body,
			&url.Priority, &url.Status, &url.RetryCount, &errorMessage,
			&url.CreatedAt, &url.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描URL数据失败: %w", err)
		}

		if body.Valid {
			url.Body = body.String
		}
		if errorMessage.Valid {
			url.ErrorMessage = errorMessage.String
		}

		urls = append(urls, url)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历URL数据失败: %w", err)
	}

	return urls, nil
}

// ResetFailedURLs 重置失败的URL为待处理状态
func (s *urlQueueServiceImpl) ResetFailedURLs(ctx context.Context, taskID string, maxRetryCount int) error {
	query := `
		UPDATE url_queue 
		SET status = ?, error_message = NULL, updated_at = NOW()
		WHERE task_id = ? AND status = ? AND retry_count < ?
	`

	result, err := s.db.ExecContext(ctx, query,
		string(models.URLStatusPending), taskID, string(models.URLStatusFailed), maxRetryCount)
	if err != nil {
		return fmt.Errorf("重置失败URL失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	fmt.Printf("成功重置 %d 个失败的URL为待处理状态，任务ID: %s\n", rowsAffected, taskID)
	return nil
}

// Close 关闭数据库连接
func (s *urlQueueServiceImpl) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}
