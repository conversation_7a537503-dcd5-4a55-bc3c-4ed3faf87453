package handlers

import (
	"net/http"
	"strconv"

	"golden_crawler/internal/models"
	"golden_crawler/internal/services"

	"github.com/gin-gonic/gin"
)

// URLQueueHandler URL队列处理器
type URLQueueHandler struct {
	urlQueueService services.URLQueueService
}

// NewURLQueueHandler 创建新的URL队列处理器
func NewURLQueueHandler(urlQueueService services.URLQueueService) *URLQueueHandler {
	return &URLQueueHandler{
		urlQueueService: urlQueueService,
	}
}

// GetPendingURLs 获取待处理的URL
// GET /api/v1/urls/pending?limit=10
func (h *URLQueueHandler) GetPendingURLs(c *gin.Context) {
	// 解析查询参数
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "无效的limit参数",
			Error:   "limit must be a positive integer",
		})
		return
	}

	// 获取待处理的URL
	urls, err := h.urlQueueService.GetPendingURLs(c.Request.Context(), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取待处理URL失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取待处理URL成功",
		"data": gin.H{
			"urls":  urls,
			"count": len(urls),
		},
	})
}

// UpdateURLStatus 更新URL状态
// PUT /api/v1/urls/:id/status
func (h *URLQueueHandler) UpdateURLStatus(c *gin.Context) {
	// 解析URL ID
	idStr := c.Param("id")
	urlID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "无效的URL ID",
			Error:   "id must be a valid integer",
		})
		return
	}

	// 解析请求体
	var req struct {
		Status       models.URLStatus `json:"status" binding:"required"`
		ErrorMessage string           `json:"error_message,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 验证状态值
	validStatuses := map[models.URLStatus]bool{
		models.URLStatusPending:    true,
		models.URLStatusProcessing: true,
		models.URLStatusCompleted:  true,
		models.URLStatusFailed:     true,
	}

	if !validStatuses[req.Status] {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "无效的状态值",
			Error:   "status must be one of: pending, processing, completed, failed",
		})
		return
	}

	// 更新URL状态
	err = h.urlQueueService.UpdateURLStatus(c.Request.Context(), urlID, req.Status, req.ErrorMessage)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "更新URL状态失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "URL状态更新成功",
	})
}

// GetTaskURLStats 获取任务的URL统计
// GET /api/v1/tasks/:taskId/urls/stats
func (h *URLQueueHandler) GetTaskURLStats(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "任务ID不能为空",
			Error:   "taskId is required",
		})
		return
	}

	// 获取任务URL统计
	stats, err := h.urlQueueService.GetTaskURLStats(c.Request.Context(), taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取任务URL统计失败",
			Error:   err.Error(),
		})
		return
	}

	// 计算总数
	total := 0
	for _, count := range stats {
		total += count
	}

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取任务URL统计成功",
		"data": gin.H{
			"task_id": taskID,
			"total":   total,
			"stats":   stats,
		},
	})
}

// QueryURLs 查询URL队列
// GET /api/v1/urls?task_id=xxx&status=pending&priority=2&limit=10&offset=0
func (h *URLQueueHandler) QueryURLs(c *gin.Context) {
	// 解析查询参数
	query := &models.URLQueueQuery{
		TaskID: c.Query("task_id"),
		Status: models.URLStatus(c.Query("status")),
	}

	// 解析优先级
	if priorityStr := c.Query("priority"); priorityStr != "" {
		priority, err := strconv.Atoi(priorityStr)
		if err != nil || priority < 1 || priority > 3 {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "无效的优先级参数",
				Error:   "priority must be 1, 2, or 3",
			})
			return
		}
		query.Priority = models.TaskPriority(priority)
	}

	// 解析分页参数
	if limitStr := c.DefaultQuery("limit", "10"); limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err != nil || limit <= 0 {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "无效的limit参数",
				Error:   "limit must be a positive integer",
			})
			return
		}
		query.Limit = limit
	}

	if offsetStr := c.DefaultQuery("offset", "0"); offsetStr != "" {
		offset, err := strconv.Atoi(offsetStr)
		if err != nil || offset < 0 {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "无效的offset参数",
				Error:   "offset must be a non-negative integer",
			})
			return
		}
		query.Offset = offset
	}

	// 查询URL队列
	urls, err := h.urlQueueService.QueryURLs(c.Request.Context(), query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "查询URL队列失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "查询URL队列成功",
		"data": gin.H{
			"urls":   urls,
			"count":  len(urls),
			"query":  query,
		},
	})
}

// ResetFailedURLs 重置失败的URL
// POST /api/v1/tasks/:taskId/urls/reset
func (h *URLQueueHandler) ResetFailedURLs(c *gin.Context) {
	taskID := c.Param("taskId")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "任务ID不能为空",
			Error:   "taskId is required",
		})
		return
	}

	// 解析请求体
	var req struct {
		MaxRetryCount int `json:"max_retry_count" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 重置失败的URL
	err := h.urlQueueService.ResetFailedURLs(c.Request.Context(), taskID, req.MaxRetryCount)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "重置失败URL失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "重置失败URL成功",
	})
}
