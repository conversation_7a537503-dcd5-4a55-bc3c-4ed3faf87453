package models

import (
	"time"
)

// HTTPMethod 定义HTTP请求方法
type HTTPMethod string

const (
	GET  HTTPMethod = "GET"
	POST HTTPMethod = "POST"
)

// ReqURL 定义初始化URL结构
type ReqURL struct {
	URL    string     `json:"url" binding:"required"`
	Method HTTPMethod `json:"method" binding:"required"`
	Body   string     `json:"body,omitempty"` // 仅当Method为POST时使用
}

// TaskPriority 定义任务优先级
type TaskPriority int

const (
	LowPriority    TaskPriority = 1
	MediumPriority TaskPriority = 2
	HighPriority   TaskPriority = 3
)

// TaskStatus 定义任务状态
type TaskStatus string

const (
	StatusPending   TaskStatus = "pending"
	StatusRunning   TaskStatus = "running"
	StatusCompleted TaskStatus = "completed"
	StatusFailed    TaskStatus = "failed"
	StatusPaused    TaskStatus = "paused"
)

// CrawlerTask 定义爬虫任务结构
type CrawlerTask struct {
	ID             string       `json:"id"`
	Name           string       `json:"name" binding:"required"`
	Description    string       `json:"description"`
	InitialURLs    []ReqURL     `json:"initial_urls" binding:"required,min=1"`
	Priority       TaskPriority `json:"priority" binding:"required,min=1,max=3"`
	DownloaderName string       `json:"downloader_name" binding:"required"`
	ParserName     string       `json:"parser_name" binding:"required"`
	Status         TaskStatus   `json:"status"`
	CreatedAt      time.Time    `json:"created_at"`
	UpdatedAt      time.Time    `json:"updated_at"`
}

// CreateTaskRequest 定义创建任务的请求结构
type CreateTaskRequest struct {
	Name           string       `json:"name" binding:"required"`
	Description    string       `json:"description"`
	InitialURLs    []ReqURL     `json:"initial_urls" binding:"required,min=1"`
	Priority       TaskPriority `json:"priority" binding:"required,min=1,max=3"`
	DownloaderName string       `json:"downloader_name" binding:"required"`
	ParserName     string       `json:"parser_name" binding:"required"`
}

// CreateTaskResponse 定义创建任务的响应结构
type CreateTaskResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	Task    *CrawlerTask `json:"task,omitempty"`
}

// URLStatus 定义URL处理状态
type URLStatus string

const (
	URLStatusPending    URLStatus = "pending"
	URLStatusProcessing URLStatus = "processing"
	URLStatusCompleted  URLStatus = "completed"
	URLStatusFailed     URLStatus = "failed"
)

// URLQueueItem 定义URL队列项结构
type URLQueueItem struct {
	ID           int64        `json:"id" db:"id"`
	TaskID       string       `json:"task_id" db:"task_id"`
	URL          string       `json:"url" db:"url"`
	Method       HTTPMethod   `json:"method" db:"method"`
	Body         string       `json:"body,omitempty" db:"body"`
	Priority     TaskPriority `json:"priority" db:"priority"`
	Status       URLStatus    `json:"status" db:"status"`
	RetryCount   int          `json:"retry_count" db:"retry_count"`
	ErrorMessage string       `json:"error_message,omitempty" db:"error_message"`
	CreatedAt    time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at" db:"updated_at"`
}

// TaskCreatedEvent 定义任务创建事件结构
type TaskCreatedEvent struct {
	TaskID    string       `json:"task_id"`
	TaskName  string       `json:"task_name"`
	Priority  TaskPriority `json:"priority"`
	URLCount  int          `json:"url_count"`
	CreatedAt time.Time    `json:"created_at"`
}

// URLQueueQuery 定义URL队列查询参数
type URLQueueQuery struct {
	TaskID   string       `json:"task_id,omitempty"`
	Status   URLStatus    `json:"status,omitempty"`
	Priority TaskPriority `json:"priority,omitempty"`
	Limit    int          `json:"limit,omitempty"`
	Offset   int          `json:"offset,omitempty"`
}

// ErrorResponse 定义错误响应结构
type ErrorResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}
